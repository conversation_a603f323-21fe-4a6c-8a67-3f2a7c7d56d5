# Transaction Calculation Integration

This document outlines the integration of backend transaction calculation logic into the frontend POS system.

## Overview

The integration implements the backend `CalculationTransactionItem` and `CalculationUpsertTransactionItem` logic in the frontend, providing:

- **Enhanced Tax Calculations**: Sales tax and service tax calculations based on product and branch settings
- **Product-Level Tax Configuration**: Support for custom tax rates per product
- **Branch-Level Default Tax Rates**: Fallback to branch default tax rates when product-specific rates are not available
- **Tax Exemption Support**: Proper handling of tax-exempt products
- **Backend-Compatible Calculations**: Frontend calculations that match backend logic exactly

## Architecture

### New Services

#### 1. TransactionService (`src/core/services/transaction.service.js`)
- **Purpose**: Handles transaction-related API calls and calculations
- **Key Methods**:
  - `calculateTransactionItem()`: Frontend implementation of backend calculation logic
  - `calculateUpsertTransactionItem()`: Handles upsert operations
  - `calculateTax()`: Core tax calculation logic (equivalent to SharedFunctionHelper.CalculateTax)
  - `getBranchByGuid()`: Retrieves branch information for default tax rates
  - `getTaxRateByGuid()`: Retrieves specific tax rate information

#### 2. Enhanced ProductService (`src/core/services/product.service.js`)
- **New Methods**:
  - `getProductByGuid()`: Retrieves product with tax-related information
  - `transformProductForTaxCalculation()`: Transforms API product data for tax calculations

### New Utilities

#### 1. TransactionCalculation (`src/core/utils/transactionCalculation.js`)
- **Purpose**: Integrates transaction calculations with existing POS order system
- **Key Functions**:
  - `calculateTransactionItemFromOrder()`: Converts POS order items to transaction calculations
  - `calculateAllTransactionItems()`: Processes all items in current order
  - `calculateEnhancedOrderTotal()`: Enhanced order total with detailed tax breakdown
  - `formatTransactionCalculation()`: Formats calculations for UI display

#### 2. Enhanced OrderStorage (`src/core/utils/orderStorage.js`)
- **Enhancements**:
  - `calculateOrderTotal()`: Now supports both basic and enhanced calculation modes
  - `calculateOrderTotalAuto()`: Automatically chooses calculation mode based on availability
  - Backward compatibility maintained with existing calculation logic

## Integration Points

### 1. POS Component (`src/feature-module/pos/pos2.jsx`)

**New State Management**:
```javascript
// Order and calculation state
const [orders, setOrders] = useState([]);
const [orderTotal, setOrderTotal] = useState({
    subtotal: 0,
    tax: 0,
    salesTax: 0,      // New: Separate sales tax
    serviceTax: 0,    // New: Separate service tax
    discount: 0,
    serviceCharges: 0,
    voucher: 0,
    roundOffAmount: 0,
    total: 0,
    calculationMode: 'basic'  // New: Tracks calculation mode
});
```

**Enhanced Product Handling**:
- `handleProductClick()`: Adds products with transaction calculation
- `handleQuantityChange()`: Updates quantities with recalculation
- `handleProductRemove()`: Removes products with recalculation
- `recalculateOrderTotal()`: Triggers enhanced calculation

**Enhanced Payment Summary**:
- Displays separate sales tax and service tax amounts
- Shows calculation mode indicator (Enhanced/Basic)
- Real-time calculation status with loading indicators
- Proper currency formatting using Malaysian Ringgit (MYR)

### 2. Backend Logic Implementation

**Tax Calculation Logic** (mirrors backend `SharedFunctionHelper.CalculateTax`):
```javascript
calculateTax(unitAmount, quantity, salesTaxRate, serviceTaxRate, isTaxExcl) {
    // Tax exclusive calculation
    if (isTaxExcl) {
        taxExclAmount = baseAmount;
        salesTaxAmount = taxExclAmount * salesRate;
        serviceTaxAmount = taxExclAmount * serviceRate;
        taxInclAmount = taxExclAmount + salesTaxAmount + serviceTaxAmount;
    } 
    // Tax inclusive calculation
    else {
        const totalTaxRate = salesRate + serviceRate;
        taxExclAmount = baseAmount / (1 + totalTaxRate);
        salesTaxAmount = taxExclAmount * salesRate;
        serviceTaxAmount = taxExclAmount * serviceRate;
        taxInclAmount = baseAmount;
    }
}
```

**Product Tax Resolution** (mirrors backend logic):
1. Check for product-specific custom sales tax rate
2. Fallback to branch default sales tax rate
3. Check for product-specific custom service tax rate
4. Fallback to branch default service tax rate (if no sales tax)
5. Validate at least one tax rate is available

## Features

### 1. Enhanced Tax Breakdown
- **Sales Tax**: Calculated separately and displayed
- **Service Tax**: Calculated separately and displayed
- **Combined Tax**: Total of sales and service tax
- **Tax-Exclusive/Inclusive**: Proper handling based on product settings

### 2. Real-Time Calculations
- **Automatic Recalculation**: Triggered on product add/remove/quantity change
- **Loading Indicators**: Shows calculation status to users
- **Error Handling**: Graceful fallback to basic calculation on errors

### 3. Calculation Modes
- **Enhanced Mode**: Uses backend-compatible transaction calculations
- **Basic Mode**: Uses existing simple tax calculation (fallback)
- **Auto Mode**: Automatically chooses best available mode

### 4. Currency Support
- **Malaysian Ringgit (MYR)**: Proper formatting throughout the system
- **Precision**: All calculations maintain 2 decimal places
- **Display**: Consistent currency formatting in UI

## Configuration

### 1. Calculation Mode Settings
```javascript
// Get current calculation mode
const mode = getCalculationMode(); // 'enhanced' or 'basic'

// Set calculation mode
setCalculationMode('enhanced');

// Check if enhanced calculation is available
const isAvailable = await isEnhancedCalculationAvailable();
```

### 2. Branch Configuration
The system requires branch information for default tax rates. Currently uses company information as fallback:

```javascript
// Mock branch structure based on company data
return {
    id: branchId,
    company: {
        defaultSalesTaxNo: company.defaultSalesTaxNo || null,
        defaultServiceTaxNo: company.defaultServiceTaxNo || null
    }
};
```

## Usage Example

### 1. Adding Product with Transaction Calculation
```javascript
const handleProductClick = async (productData) => {
    // Create order item
    const orderItem = {
        id: productData.id,
        productId: productData.id,
        name: productData.name,
        price: productData.price,
        quantity: 1
    };

    // Add to orders with transaction calculation
    addProductToOrders(orderItem);
    await recalculateOrderTotal();
};
```

### 2. Enhanced Payment Summary Display
```jsx
<tr>
    <td>Sales Tax</td>
    <td className="text-gray-9 text-end">
        {formatCurrency(orderTotal.salesTax)}
    </td>
</tr>
<tr>
    <td>Service Tax</td>
    <td className="text-gray-9 text-end">
        {formatCurrency(orderTotal.serviceTax)}
    </td>
</tr>
```

## Benefits

1. **Accuracy**: Frontend calculations now match backend exactly
2. **Transparency**: Users can see detailed tax breakdown
3. **Flexibility**: Supports both product-specific and branch-default tax rates
4. **Compatibility**: Maintains backward compatibility with existing system
5. **Performance**: Calculations happen in real-time without API calls for each change
6. **Reliability**: Graceful fallback to basic calculation if enhanced mode fails

## Future Enhancements

1. **API Integration**: Direct integration with transaction calculation APIs
2. **Caching**: Cache product tax information for better performance
3. **Validation**: Add validation for tax rate configurations
4. **Reporting**: Enhanced reporting with detailed tax breakdowns
5. **Multi-Currency**: Support for multiple currencies beyond MYR

## Testing

To test the integration:

1. **Product Addition**: Click on the iPhone 14 64GB product (enhanced with transaction calculation)
2. **Tax Display**: Check payment summary for separate sales/service tax amounts
3. **Calculation Mode**: Look for "Enhanced" badge in payment summary
4. **Currency Format**: Verify amounts are displayed in MYR format
5. **Real-time Updates**: Verify calculations update when quantities change

## Troubleshooting

### Common Issues

1. **Enhanced Mode Not Available**: Check browser console for initialization errors
2. **Calculation Errors**: System will fallback to basic mode automatically
3. **Missing Tax Rates**: Verify company/branch tax configuration
4. **Currency Display**: Ensure formatCurrency utility is properly imported

### Debug Information

The system logs detailed information to browser console:
- Calculation mode initialization
- Product addition with transaction calculation
- Tax calculation results
- Fallback scenarios

Check browser console for detailed debug information during development.
