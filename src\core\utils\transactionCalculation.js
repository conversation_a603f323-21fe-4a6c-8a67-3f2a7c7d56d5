/**
 * Transaction Calculation Utility
 * Frontend implementation of backend transaction calculation logic
 * Integrates with existing POS order storage and calculation system
 */

import TransactionService from '../services/transaction.service';
import { getOrders } from './orderStorage';
import { formatCurrency } from './currency';

/**
 * Calculate transaction item with backend-compatible logic
 * @param {Object} orderItem - Order item from POS system
 * @param {string} branchId - Branch ID (optional, uses default if not provided)
 * @returns {Promise<Object>} - Transaction calculation result
 */
export const calculateTransactionItemFromOrder = async (orderItem, branchId = null) => {
  try {
    // Use default branch ID if not provided
    const effectiveBranchId = branchId || TransactionService.getDefaultBranchId();

    // Transform order item to transaction item request format
    const transactionItemRequest = {
      productId: orderItem.productId || orderItem.id,
      unitAmount: parseFloat(orderItem.price) || 0,
      quantity: parseInt(orderItem.quantity) || 1,
      totalUnitAmountWOTax: parseFloat(orderItem.totalUnitAmountWOTax) || 0,
      totalUnitAmount: parseFloat(orderItem.totalUnitAmount) || 0,
      subTotalAmount: parseFloat(orderItem.subTotalAmount) || 0
    };

    // Validate the request
    if (!TransactionService.validateTransactionItemRequest(transactionItemRequest)) {
      throw new Error('Invalid transaction item request');
    }

    // Calculate using transaction service
    const calculation = await TransactionService.calculateTransactionItem(
      transactionItemRequest,
      effectiveBranchId
    );

    return calculation;
  } catch (error) {
    console.error('Error calculating transaction item from order:', error);
    
    // Return fallback calculation
    return getFallbackCalculation(orderItem);
  }
};

/**
 * Calculate all transaction items in current order
 * @param {string} branchId - Branch ID (optional)
 * @returns {Promise<Array>} - Array of transaction calculation results
 */
export const calculateAllTransactionItems = async (branchId = null) => {
  try {
    const orders = getOrders();
    const calculations = [];

    for (const orderItem of orders) {
      try {
        const calculation = await calculateTransactionItemFromOrder(orderItem, branchId);
        calculations.push({
          orderItem,
          calculation,
          success: true
        });
      } catch (error) {
        console.error(`Error calculating item ${orderItem.id}:`, error);
        calculations.push({
          orderItem,
          calculation: getFallbackCalculation(orderItem),
          success: false,
          error: error.message
        });
      }
    }

    return calculations;
  } catch (error) {
    console.error('Error calculating all transaction items:', error);
    return [];
  }
};

/**
 * Get enhanced order total with transaction-level calculations
 * @param {Object} options - Calculation options
 * @param {string} branchId - Branch ID (optional)
 * @returns {Promise<Object>} - Enhanced order total with detailed tax breakdown
 */
export const calculateEnhancedOrderTotal = async (options = {}, branchId = null) => {
  try {
    // Get all transaction calculations
    const transactionCalculations = await calculateAllTransactionItems(branchId);

    // Calculate totals from transaction calculations
    let totalSalesTax = 0;
    let totalServiceTax = 0;
    let totalTaxExclAmount = 0;
    let totalTaxInclAmount = 0;
    let subtotalWOTax = 0;
    let subtotalWithTax = 0;

    transactionCalculations.forEach(({ calculation }) => {
      totalSalesTax += calculation.salesTaxAmount || 0;
      totalServiceTax += calculation.serviceTaxAmount || 0;
      totalTaxExclAmount += calculation.taxExclAmount || 0;
      totalTaxInclAmount += calculation.taxInclAmount || 0;
      subtotalWOTax += calculation.totalUnitAmountWOTax || 0;
      subtotalWithTax += calculation.subTotalAmount || 0;
    });

    // Apply additional charges and discounts
    const discountType = options.discountType || 'percentage';
    let discountValue = parseFloat(options.discountValue || 0);
    
    if (discountType === 'percentage' && options.customPercentage !== undefined) {
      discountValue = parseFloat(options.customPercentage);
    }

    // Calculate discount amount
    let discountAmount = 0;
    if (discountType === 'flat') {
      discountAmount = discountValue;
    } else if (discountType === 'percentage') {
      discountAmount = (subtotalWithTax * discountValue) / 100;
    }

    // Ensure discount doesn't exceed subtotal
    discountAmount = Math.min(discountAmount, subtotalWithTax);

    // Get other charges
    const serviceCharges = parseFloat(options.serviceCharges || 0);
    const voucherAmount = parseFloat(options.voucher || 0);

    // Calculate final total
    let finalTotal = subtotalWithTax - discountAmount + serviceCharges - voucherAmount;

    // Apply rounding if enabled
    let roundOffAmount = 0;
    if (options.roundOff) {
      const roundedTotal = Math.round(finalTotal);
      roundOffAmount = roundedTotal - finalTotal;
      finalTotal = roundedTotal;
    }

    // Ensure total is not negative
    finalTotal = Math.max(0, finalTotal);

    return {
      // Transaction-level calculations
      salesTax: parseFloat(totalSalesTax.toFixed(2)),
      serviceTax: parseFloat(totalServiceTax.toFixed(2)),
      totalTax: parseFloat((totalSalesTax + totalServiceTax).toFixed(2)),
      
      // Subtotals
      subtotalExclTax: parseFloat(totalTaxExclAmount.toFixed(2)),
      subtotalInclTax: parseFloat(totalTaxInclAmount.toFixed(2)),
      subtotal: parseFloat(subtotalWithTax.toFixed(2)),
      
      // Adjustments
      discount: parseFloat(discountAmount.toFixed(2)),
      serviceCharges: parseFloat(serviceCharges.toFixed(2)),
      voucher: parseFloat(voucherAmount.toFixed(2)),
      roundOffAmount: parseFloat(roundOffAmount.toFixed(2)),
      
      // Final total
      total: parseFloat(finalTotal.toFixed(2)),
      
      // Metadata
      discountType,
      discountValue,
      taxableAmount: parseFloat((subtotalWithTax - discountAmount).toFixed(2)),
      
      // Transaction calculations for debugging
      transactionCalculations: transactionCalculations.map(calc => ({
        itemId: calc.orderItem.id,
        itemName: calc.orderItem.name,
        calculation: calc.calculation,
        success: calc.success,
        error: calc.error
      }))
    };
  } catch (error) {
    console.error('Error calculating enhanced order total:', error);
    
    // Fallback to basic calculation
    const { calculateOrderTotal } = await import('./orderStorage');
    return calculateOrderTotal(options);
  }
};

/**
 * Get fallback calculation for when API calls fail
 * @param {Object} orderItem - Order item
 * @returns {Object} - Fallback calculation result
 */
const getFallbackCalculation = (orderItem) => {
  const unitAmount = parseFloat(orderItem.price) || 0;
  const quantity = parseInt(orderItem.quantity) || 1;
  const totalAmount = unitAmount * quantity;

  return {
    salesTaxAmount: 0,
    serviceTaxAmount: 0,
    taxExclAmount: unitAmount,
    taxInclAmount: unitAmount,
    totalUnitAmountWOTax: unitAmount,
    totalUnitAmount: unitAmount,
    subTotalAmount: totalAmount
  };
};

/**
 * Format transaction calculation for display
 * @param {Object} calculation - Transaction calculation result
 * @returns {Object} - Formatted calculation for UI display
 */
export const formatTransactionCalculation = (calculation) => {
  return {
    salesTax: formatCurrency(calculation.salesTaxAmount),
    serviceTax: formatCurrency(calculation.serviceTaxAmount),
    totalTax: formatCurrency((calculation.salesTaxAmount || 0) + (calculation.serviceTaxAmount || 0)),
    taxExclAmount: formatCurrency(calculation.taxExclAmount),
    taxInclAmount: formatCurrency(calculation.taxInclAmount),
    unitAmountWOTax: formatCurrency(calculation.totalUnitAmountWOTax),
    unitAmount: formatCurrency(calculation.totalUnitAmount),
    subTotal: formatCurrency(calculation.subTotalAmount)
  };
};

/**
 * Check if enhanced calculation is available
 * @returns {Promise<boolean>} - Whether enhanced calculation can be used
 */
export const isEnhancedCalculationAvailable = async () => {
  try {
    // Test if we can access the transaction service
    const branchId = TransactionService.getDefaultBranchId();
    return !!branchId;
  } catch (error) {
    console.warn('Enhanced calculation not available:', error);
    return false;
  }
};

/**
 * Get calculation mode preference
 * @returns {string} - 'enhanced' or 'basic'
 */
export const getCalculationMode = () => {
  try {
    return localStorage.getItem('pos_calculation_mode') || 'enhanced';
  } catch (error) {
    return 'enhanced';
  }
};

/**
 * Set calculation mode preference
 * @param {string} mode - 'enhanced' or 'basic'
 */
export const setCalculationMode = (mode) => {
  try {
    localStorage.setItem('pos_calculation_mode', mode);
  } catch (error) {
    console.error('Error setting calculation mode:', error);
  }
};
