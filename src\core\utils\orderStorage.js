/**
 * Utility functions for managing orders in local storage
 */

// Local storage keys
const ORDERS_STORAGE_KEY = 'pos_orders';
const DISCOUNT_SETTINGS_KEY = 'pos_discount_settings';
const PAYMENT_SUMMARY_KEY = 'pos_payment_summary';

/**
 * Get all orders from local storage
 * @returns {Array} Array of order items
 */
export const getOrders = () => {
  try {
    const orders = localStorage.getItem(ORDERS_STORAGE_KEY);
    return orders ? JSON.parse(orders) : [];
  } catch (error) {
    console.error('Error retrieving orders from local storage:', error);
    return [];
  }
};

/**
 * Save orders to local storage
 * @param {Array} orders - Array of order items
 */
export const saveOrders = (orders) => {
  try {
    localStorage.setItem(ORDERS_STORAGE_KEY, JSON.stringify(orders));
  } catch (error) {
    console.error('Error saving orders to local storage:', error);
  }
};

/**
 * Add a product to orders
 * @param {Object} product - Product to add to orders
 * @returns {Array} Updated orders array
 */
export const addProductToOrders = (product) => {
  try {
    const orders = getOrders();

    // Check if product already exists in orders
    const existingProductIndex = orders.findIndex(item => item.id === product.id);

    if (existingProductIndex !== -1) {
      // If product exists, increment quantity
      orders[existingProductIndex].quantity += 1;
    } else {
      // If product doesn't exist, add it with quantity 1
      const defaultUom = product.productUOM?.find(uom => uom.isMainUom === true) ||
                         (product.productUOM?.length > 0 ? product.productUOM[0] : null);

      const price = defaultUom?.effectivedProductPrice?.price || 0;
      const formattedPrice = price.toFixed(product.currency?.precision || 2);

      orders.push({
        id: product.id,
        name: product.name,
        image: product.image || product.img || "assets/img/products/pos-product-01.svg",
        price: formattedPrice,
        currencySymbol: product.currency?.symbol || '$',
        quantity: 1,
        uom: defaultUom?.uomPrimary?.name || 'Pcs',
        fractionQty: defaultUom?.effectivedProductPrice?.fractionQty || 1
      });
    }

    saveOrders(orders);
    return orders;
  } catch (error) {
    console.error('Error adding product to orders:', error);
    return getOrders();
  }
};

/**
 * Update product quantity in orders
 * @param {string|number} productId - ID of the product to update
 * @param {number} quantity - New quantity
 * @returns {Array} Updated orders array
 */
export const updateProductQuantity = (productId, quantity) => {
  try {
    const orders = getOrders();
    const productIndex = orders.findIndex(item => item.id === productId);

    if (productIndex !== -1) {
      if (quantity <= 0) {
        // Remove product if quantity is 0 or negative
        orders.splice(productIndex, 1);
      } else {
        // Update quantity
        orders[productIndex].quantity = quantity;
      }

      saveOrders(orders);
    }

    return orders;
  } catch (error) {
    console.error('Error updating product quantity:', error);
    return getOrders();
  }
};

/**
 * Remove a product from orders
 * @param {string|number} productId - ID of the product to remove
 * @returns {Array} Updated orders array
 */
export const removeProductFromOrders = (productId) => {
  try {
    const orders = getOrders();
    const updatedOrders = orders.filter(item => item.id !== productId);

    saveOrders(updatedOrders);
    return updatedOrders;
  } catch (error) {
    console.error('Error removing product from orders:', error);
    return getOrders();
  }
};

/**
 * Clear all orders
 * @returns {Array} Empty array
 */
export const clearOrders = () => {
  try {
    localStorage.removeItem(ORDERS_STORAGE_KEY);
    return [];
  } catch (error) {
    console.error('Error clearing orders:', error);
    return getOrders();
  }
};

/**
 * Save discount settings to local storage
 * @param {Object} settings - Discount settings object
 */
export const saveDiscountSettings = (settings) => {
  try {
    localStorage.setItem(DISCOUNT_SETTINGS_KEY, JSON.stringify(settings));
  } catch (error) {
    console.error('Error saving discount settings to local storage:', error);
  }
};

/**
 * Get discount settings from local storage
 * @returns {Object} Discount settings object
 */
export const getDiscountSettings = () => {
  try {
    const settings = localStorage.getItem(DISCOUNT_SETTINGS_KEY);
    return settings ? JSON.parse(settings) : {
      discountType: 'percentage',
      discountValue: 0,
      customPercentage: 0
    };
  } catch (error) {
    console.error('Error retrieving discount settings from local storage:', error);
    return {
      discountType: 'percentage',
      discountValue: 0,
      customPercentage: 0
    };
  }
};

/**
 * Save payment summary settings to local storage
 * @param {Object} settings - Payment summary settings object
 */
export const savePaymentSummary = (settings) => {
  try {
    localStorage.setItem(PAYMENT_SUMMARY_KEY, JSON.stringify(settings));
  } catch (error) {
    console.error('Error saving payment summary to local storage:', error);
  }
};

/**
 * Get payment summary settings from local storage
 * @returns {Object} Payment summary settings object
 */
export const getPaymentSummary = () => {
  try {
    const settings = localStorage.getItem(PAYMENT_SUMMARY_KEY);
    return settings ? JSON.parse(settings) : {
      serviceCharges: 0,
      tax: 0,
      taxRate: 0,
      voucher: 0,
      roundOff: true,
      roundOffAmount: 0
    };
  } catch (error) {
    console.error('Error retrieving payment summary from local storage:', error);
    return {
      serviceCharges: 0,
      tax: 0,
      taxRate: 0,
      voucher: 0,
      roundOff: true,
      roundOffAmount: 0
    };
  }
};

/**
 * Calculate order total with enhanced transaction calculation support
 * @param {Object} options - Options for calculation (tax rate, discount, etc.)
 * @param {boolean} useEnhancedCalculation - Whether to use enhanced transaction calculation
 * @returns {Object|Promise<Object>} Object containing subtotal, tax, discount, and total
 */
export const calculateOrderTotal = (options = {}, useEnhancedCalculation = false) => {
  // If enhanced calculation is requested, return a promise
  if (useEnhancedCalculation) {
    return calculateOrderTotalEnhanced(options);
  }

  // Otherwise, use the existing synchronous calculation
  return calculateOrderTotalBasic(options);
};

/**
 * Basic order total calculation (existing logic)
 * @param {Object} options - Options for calculation
 * @returns {Object} Object containing subtotal, tax, discount, and total
 */
const calculateOrderTotalBasic = (options = {}) => {
  try {
    const orders = getOrders();

    // Calculate subtotal
    const subtotal = orders.reduce((total, item) => {
      return total + (parseFloat(item.price) * item.quantity);
    }, 0);

    // Get saved discount settings if no options provided
    const savedSettings = getDiscountSettings();

    // Get saved payment summary settings if no options provided
    const savedPaymentSummary = getPaymentSummary();

    // Get discount from options or use saved settings
    const discountType = options.discountType || savedSettings.discountType || 'percentage';
    let discountValue = parseFloat(options.discountValue || savedSettings.discountValue || 0);

    // Use custom percentage if available and discount type is percentage
    if (discountType === 'percentage' && options.customPercentage !== undefined) {
      discountValue = parseFloat(options.customPercentage);
    } else if (discountType === 'percentage' && savedSettings.customPercentage) {
      discountValue = parseFloat(savedSettings.customPercentage);
    }

    // Calculate discount amount
    let discountAmount = 0;
    if (discountType === 'flat') {
      discountAmount = discountValue;
    } else if (discountType === 'percentage') {
      discountAmount = (subtotal * discountValue) / 100;
    }

    // Ensure discount doesn't exceed subtotal
    discountAmount = Math.min(discountAmount, subtotal);

    // Get service charges from options or use saved settings
    const serviceCharges = parseFloat(options.serviceCharges !== undefined ? options.serviceCharges : savedPaymentSummary.serviceCharges || 0);

    // Get tax rate from options or use saved settings
    const taxRate = parseFloat(options.taxRate !== undefined ? options.taxRate : savedPaymentSummary.taxRate || 0);

    // Calculate amount after discount (this is the taxable amount)
    const taxableAmount = subtotal - discountAmount;

    // Calculate tax amount on taxable amount (after discount)
    const taxAmount = (taxableAmount * taxRate) / 100;

    // Get voucher amount from options or use saved settings
    const voucherAmount = parseFloat(options.voucher !== undefined ? options.voucher : savedPaymentSummary.voucher || 0);

    // Calculate total before rounding
    // Formula: Taxable Amount + Tax + Service Charges - Voucher
    let total = taxableAmount + taxAmount + serviceCharges - voucherAmount;

    // Apply rounding if enabled
    const roundOff = options.roundOff !== undefined ? options.roundOff : savedPaymentSummary.roundOff;
    let roundOffAmount = 0;

    if (roundOff) {
      // Round to nearest whole number
      const roundedTotal = Math.round(total);
      roundOffAmount = roundedTotal - total;
      total = roundedTotal;
    }

    // Ensure total is not negative
    total = Math.max(0, total);

    return {
      subtotal: parseFloat(subtotal.toFixed(2)),
      tax: parseFloat(taxAmount.toFixed(2)),
      taxRate: taxRate,
      discount: parseFloat(discountAmount.toFixed(2)),
      serviceCharges: parseFloat(serviceCharges.toFixed(2)),
      voucher: parseFloat(voucherAmount.toFixed(2)),
      roundOffAmount: parseFloat(roundOffAmount.toFixed(2)),
      total: parseFloat(total.toFixed(2)),
      discountType,
      discountValue,
      taxableAmount: parseFloat(taxableAmount.toFixed(2))
    };
  } catch (error) {
    console.error('Error calculating order total:', error);
    return {
      subtotal: 0,
      tax: 0,
      taxRate: 0,
      discount: 0,
      serviceCharges: 0,
      voucher: 0,
      roundOffAmount: 0,
      total: 0,
      discountType: 'percentage',
      discountValue: 0,
      taxableAmount: 0
    };
  }
};

/**
 * Enhanced order total calculation using transaction calculation logic
 * @param {Object} options - Options for calculation
 * @returns {Promise<Object>} Promise resolving to order total with enhanced tax calculations
 */
const calculateOrderTotalEnhanced = async (options = {}) => {
  try {
    // Import the enhanced calculation utility
    const { calculateEnhancedOrderTotal, isEnhancedCalculationAvailable } = await import('./transactionCalculation');

    // Check if enhanced calculation is available
    const isAvailable = await isEnhancedCalculationAvailable();
    if (!isAvailable) {
      console.warn('Enhanced calculation not available, falling back to basic calculation');
      return calculateOrderTotalBasic(options);
    }

    // Use enhanced calculation
    const enhancedResult = await calculateEnhancedOrderTotal(options);

    // Transform enhanced result to match existing interface
    return {
      subtotal: enhancedResult.subtotal,
      tax: enhancedResult.totalTax,
      salesTax: enhancedResult.salesTax,
      serviceTax: enhancedResult.serviceTax,
      taxRate: enhancedResult.totalTax > 0 ? ((enhancedResult.totalTax / enhancedResult.subtotalExclTax) * 100) : 0,
      discount: enhancedResult.discount,
      serviceCharges: enhancedResult.serviceCharges,
      voucher: enhancedResult.voucher,
      roundOffAmount: enhancedResult.roundOffAmount,
      total: enhancedResult.total,
      discountType: enhancedResult.discountType,
      discountValue: enhancedResult.discountValue,
      taxableAmount: enhancedResult.taxableAmount,

      // Additional enhanced fields
      subtotalExclTax: enhancedResult.subtotalExclTax,
      subtotalInclTax: enhancedResult.subtotalInclTax,
      transactionCalculations: enhancedResult.transactionCalculations,
      calculationMode: 'enhanced'
    };
  } catch (error) {
    console.error('Error in enhanced order total calculation, falling back to basic:', error);
    return calculateOrderTotalBasic(options);
  }
};

/**
 * Get calculation mode preference and calculate accordingly
 * @param {Object} options - Options for calculation
 * @returns {Object|Promise<Object>} Order total calculation result
 */
export const calculateOrderTotalAuto = async (options = {}) => {
  try {
    const { getCalculationMode, isEnhancedCalculationAvailable } = await import('./transactionCalculation');

    const mode = getCalculationMode();
    const isEnhanced = mode === 'enhanced' && await isEnhancedCalculationAvailable();

    return calculateOrderTotal(options, isEnhanced);
  } catch (error) {
    console.error('Error in auto calculation mode, using basic:', error);
    return calculateOrderTotalBasic(options);
  }
};
