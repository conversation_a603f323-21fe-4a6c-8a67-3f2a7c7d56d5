/**
 * Product Service
 * Handles API calls related to products
 */

import { handleApiResponse } from '../utils/api.utils';
import { apiGet, apiPost, apiPut, apiDelete } from '../utils/api.interceptor';

/**
 * Service for handling product-related API operations
 */
class ProductService {
  /**
   * Get all products
   * @param {Object} options - Additional fetch options
   * @returns {Promise} - Promise with products data
   */
  async getProducts(options = {}) {
    try {
      const response = await apiGet('/product', options);
      return handleApiResponse(response);
    } catch (error) {
      console.error('Error fetching products:', error);
      throw error;
    }
  }

  /**
   * Get product by GUID (required for transaction calculations)
   * @param {string} productId - Product GUID
   * @param {Object} options - Additional fetch options
   * @returns {Promise} - Promise with product data including tax information
   */
  async getProductByGuid(productId, options = {}) {
    try {
      const response = await apiGet(`/product/${productId}`, options);
      const product = handleApiResponse(response);

      // Transform API response to include tax-related fields
      return this.transformProductForTaxCalculation(product);
    } catch (error) {
      console.error('Error fetching product by GUID:', error);
      throw error;
    }
  }

  /**
   * Transform product data to include tax calculation fields
   * @param {Object} apiProduct - Product data from API
   * @returns {Object} - Transformed product data
   */
  transformProductForTaxCalculation(apiProduct) {
    if (!apiProduct) {
      return null;
    }

    return {
      id: apiProduct.id,
      name: apiProduct.name || apiProduct.productName,
      price: apiProduct.price || apiProduct.unitPrice || 0,
      isTaxExempt: apiProduct.isTaxExempt || false,
      isTaxExcl: apiProduct.isTaxExcl !== undefined ? apiProduct.isTaxExcl : true, // Default to tax exclusive
      customSalesTaxNoId: apiProduct.customSalesTaxNoId || null,
      customServiceTaxNoId: apiProduct.customServiceTaxNoId || null,
      category: apiProduct.category,
      description: apiProduct.description,
      sku: apiProduct.sku || apiProduct.productCode,
      barcode: apiProduct.barcode,
      stock: apiProduct.stock || apiProduct.quantity || 0,
      unit: apiProduct.unit || 'pcs',
      image: apiProduct.image || apiProduct.productImage,
      isActive: apiProduct.isActive !== undefined ? apiProduct.isActive : true,
      createdAt: apiProduct.createdAt,
      updatedAt: apiProduct.updatedAt
    };
  }

  /**
   * Get products by category ID
   * @param {string} categoryId - Category ID
   * @param {Object} options - Additional fetch options
   * @returns {Promise} - Promise with filtered products data
   */
  async getProductsByCategory(categoryId, options = {}) {
    try {
      const response = await apiGet(`/product?categoryId=${categoryId}`, options);
      return handleApiResponse(response);
    } catch (error) {
      console.error(`Error fetching products for category ${categoryId}:`, error);
      throw error;
    }
  }

  /**
   * Get a product by ID
   * @param {string} productId - Product ID
   * @param {Object} options - Additional fetch options
   * @returns {Promise} - Promise with product data
   */
  async getProductById(productId, options = {}) {
    try {
      const response = await apiGet(`/product/${productId}`, options);
      return handleApiResponse(response);
    } catch (error) {
      console.error(`Error fetching product with ID ${productId}:`, error);
      throw error;
    }
  }

  /**
   * Create a new product
   * @param {Object} productData - Product data
   * @param {Object} options - Additional fetch options
   * @returns {Promise} - Promise with created product data
   */
  async createProduct(productData, options = {}) {
    try {
      const response = await apiPost('/product/create', productData, options);
      return handleApiResponse(response);
    } catch (error) {
      console.error('Error creating product:', error);
      throw error;
    }
  }

  /**
   * Update a product
   * @param {Object} productData - Product data with ID
   * @param {Object} options - Additional fetch options
   * @returns {Promise} - Promise with updated product data
   */
  async updateProduct(productData, options = {}) {
    try {
      const response = await apiPut('/product/update', productData, options);
      return handleApiResponse(response);
    } catch (error) {
      console.error(`Error updating product with ID ${productData.id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a product
   * @param {string} productId - Product ID
   * @param {Object} options - Additional fetch options
   * @returns {Promise} - Promise with deletion result
   */
  async deleteProduct(productId, options = {}) {
    try {
      const response = await apiDelete(`/product/delete?productId=${productId}`, options);
      return handleApiResponse(response);
    } catch (error) {
      console.error(`Error deleting product with ID ${productId}:`, error);
      throw error;
    }
  }

  /**
   * Search products
   * @param {string} query - Search query
   * @param {Object} options - Additional fetch options
   * @returns {Promise} - Promise with search results
   */
  async searchProducts(query, options = {}) {
    try {
      const response = await apiGet(`/product/search?query=${encodeURIComponent(query)}`, options);
      return handleApiResponse(response);
    } catch (error) {
      console.error(`Error searching products with query "${query}":`, error);
      throw error;
    }
  }
}

// Create and export a singleton instance
const productService = new ProductService();
export default productService;
