/**
 * Transaction Service
 * Handles API calls related to transactions and transaction calculations
 */

import { handleApiResponse } from '../utils/api.utils';
import { apiGet, apiPost, apiPut } from '../utils/api.interceptor';
import ProductService from './product.service';
import CompanyService from './company.service';

/**
 * Service for handling transaction-related API operations
 */
class TransactionService {
  /**
   * Get transaction by ID
   * @param {string} transactionId - Transaction ID
   * @param {Object} options - Additional fetch options
   * @returns {Promise} - Promise with transaction data
   */
  async getTransaction(transactionId, options = {}) {
    try {
      const response = await apiGet(`/transaction/transaction/get?transactionId=${transactionId}`, options);
      return handleApiResponse(response);
    } catch (error) {
      console.error('Error fetching transaction:', error);
      throw error;
    }
  }

  /**
   * Create a new transaction
   * @param {Object} transactionData - Transaction data
   * @param {boolean} isCalculationRequired - Whether calculation is required
   * @param {Object} options - Additional fetch options
   * @returns {Promise} - Promise with created transaction data
   */
  async createTransaction(transactionData, isCalculationRequired = true, options = {}) {
    try {
      const response = await apiPost(
        `/transaction/transaction/create?isCalculationRequired=${isCalculationRequired}`,
        transactionData,
        options
      );
      return handleApiResponse(response);
    } catch (error) {
      console.error('Error creating transaction:', error);
      throw error;
    }
  }

  /**
   * Update an existing transaction
   * @param {Object} transactionData - Updated transaction data
   * @param {boolean} isCalculationRequired - Whether calculation is required
   * @param {Object} options - Additional fetch options
   * @returns {Promise} - Promise with updated transaction data
   */
  async updateTransaction(transactionData, isCalculationRequired = true, options = {}) {
    try {
      const response = await apiPost(
        `/transaction/transaction/update?isCalculationRequired=${isCalculationRequired}`,
        transactionData,
        options
      );
      return handleApiResponse(response);
    } catch (error) {
      console.error('Error updating transaction:', error);
      throw error;
    }
  }

  /**
   * Calculate transaction item (frontend implementation of backend logic)
   * @param {Object} transactionItemRequest - Transaction item request
   * @param {string} branchId - Branch ID
   * @returns {Promise<Object>} - Promise with transaction calculation result
   */
  async calculateTransactionItem(transactionItemRequest, branchId) {
    try {
      const transactionItemCalculate = {
        salesTaxAmount: 0,
        serviceTaxAmount: 0,
        taxExclAmount: 0,
        taxInclAmount: 0,
        totalUnitAmountWOTax: 0,
        totalUnitAmount: 0,
        subTotalAmount: 0
      };

      // Get product information
      const product = await ProductService.getProductByGuid(transactionItemRequest.productId);
      if (!product) {
        throw new Error(`Product ${transactionItemRequest.productId} not found.`);
      }

      // If product is tax exempt, use provided amounts
      if (product.isTaxExempt) {
        transactionItemCalculate.totalUnitAmountWOTax = transactionItemRequest.totalUnitAmountWOTax || 0;
        transactionItemCalculate.totalUnitAmount = transactionItemRequest.totalUnitAmount || 0;
        transactionItemCalculate.subTotalAmount = transactionItemRequest.subTotalAmount || 0;
        return transactionItemCalculate;
      }

      // Get branch information for default tax rates
      const branch = await this.getBranchByGuid(branchId);
      if (!branch) {
        throw new Error('Branch not found.');
      }

      // Determine sales tax rate
      let salesTaxRate = null;
      if (product.customSalesTaxNoId) {
        const salesTaxNo = await this.getTaxRateByGuid(product.customSalesTaxNoId);
        if (!salesTaxNo) {
          throw new Error(`Product custom sales tax no id ${product.customSalesTaxNoId} not found.`);
        }
        salesTaxRate = salesTaxNo.chargePercentage;
      } else if (branch.company?.defaultSalesTaxNo) {
        salesTaxRate = branch.company.defaultSalesTaxNo.chargePercentage;
      }

      // Determine service tax rate
      let serviceTaxRate = null;
      if (product.customServiceTaxNoId) {
        const serviceTaxNo = await this.getTaxRateByGuid(product.customServiceTaxNoId);
        if (!serviceTaxNo) {
          throw new Error(`Product custom service tax no id ${product.customServiceTaxNoId} not found.`);
        }
        serviceTaxRate = serviceTaxNo.chargePercentage;
      } else if (!salesTaxRate && branch.company?.defaultServiceTaxNo) {
        serviceTaxRate = branch.company.defaultServiceTaxNo.chargePercentage;
      }

      // Validate that at least one tax rate is available
      if (salesTaxRate === null && serviceTaxRate === null) {
        throw new Error('Branch default sales tax and service tax no not found.');
      }

      // Calculate tax amounts using shared function helper logic
      const calculationResult = this.calculateTax(
        transactionItemRequest.unitAmount,
        transactionItemRequest.quantity,
        salesTaxRate,
        serviceTaxRate,
        product.isTaxExcl
      );

      // Map calculation results
      transactionItemCalculate.salesTaxAmount = calculationResult.salesTaxAmount;
      transactionItemCalculate.serviceTaxAmount = calculationResult.serviceTaxAmount;
      transactionItemCalculate.taxExclAmount = calculationResult.taxExclAmount;
      transactionItemCalculate.taxInclAmount = calculationResult.taxInclAmount;
      transactionItemCalculate.totalUnitAmountWOTax = calculationResult.totalUnitAmountWOTax;
      transactionItemCalculate.totalUnitAmount = calculationResult.totalUnitAmount;
      transactionItemCalculate.subTotalAmount = calculationResult.subTotalAmount;

      return transactionItemCalculate;
    } catch (error) {
      console.error('Error calculating transaction item:', error);
      throw error;
    }
  }

  /**
   * Get branch by GUID
   * @param {string} branchId - Branch ID
   * @returns {Promise<Object>} - Promise with branch data
   */
  async getBranchByGuid(branchId) {
    try {
      // This would typically call a branch API endpoint
      // For now, we'll use company service to get current company info
      const company = await CompanyService.getCurrentCompany();
      
      // Mock branch structure based on company data
      return {
        id: branchId,
        company: {
          defaultSalesTaxNo: company.defaultSalesTaxNo || null,
          defaultServiceTaxNo: company.defaultServiceTaxNo || null
        }
      };
    } catch (error) {
      console.error('Error fetching branch:', error);
      throw error;
    }
  }

  /**
   * Get tax rate by GUID
   * @param {string} taxRateId - Tax rate ID
   * @returns {Promise<Object>} - Promise with tax rate data
   */
  async getTaxRateByGuid(taxRateId) {
    try {
      const response = await apiGet(`/company/taxcategory/${taxRateId}`);
      return handleApiResponse(response);
    } catch (error) {
      console.error('Error fetching tax rate:', error);
      throw error;
    }
  }

  /**
   * Calculate tax amounts (frontend implementation of SharedFunctionHelper.CalculateTax)
   * @param {number} unitAmount - Unit amount
   * @param {number} quantity - Quantity
   * @param {number|null} salesTaxRate - Sales tax rate percentage
   * @param {number|null} serviceTaxRate - Service tax rate percentage
   * @param {boolean} isTaxExcl - Whether the amount is tax exclusive
   * @returns {Object} - Tax calculation results
   */
  calculateTax(unitAmount, quantity, salesTaxRate, serviceTaxRate, isTaxExcl) {
    try {
      const baseAmount = parseFloat(unitAmount) || 0;
      const qty = parseInt(quantity) || 1;
      const salesRate = salesTaxRate ? parseFloat(salesTaxRate) / 100 : 0;
      const serviceRate = serviceTaxRate ? parseFloat(serviceTaxRate) / 100 : 0;

      let salesTaxAmount = 0;
      let serviceTaxAmount = 0;
      let taxExclAmount = 0;
      let taxInclAmount = 0;
      let totalUnitAmountWOTax = 0;
      let totalUnitAmount = 0;
      let subTotalAmount = 0;

      if (isTaxExcl) {
        // Tax exclusive calculation
        taxExclAmount = baseAmount;
        salesTaxAmount = taxExclAmount * salesRate;
        serviceTaxAmount = taxExclAmount * serviceRate;
        taxInclAmount = taxExclAmount + salesTaxAmount + serviceTaxAmount;

        totalUnitAmountWOTax = taxExclAmount;
        totalUnitAmount = taxInclAmount;
        subTotalAmount = totalUnitAmount * qty;
      } else {
        // Tax inclusive calculation
        const totalTaxRate = salesRate + serviceRate;
        taxExclAmount = baseAmount / (1 + totalTaxRate);
        salesTaxAmount = taxExclAmount * salesRate;
        serviceTaxAmount = taxExclAmount * serviceRate;
        taxInclAmount = baseAmount;

        totalUnitAmountWOTax = taxExclAmount;
        totalUnitAmount = taxInclAmount;
        subTotalAmount = totalUnitAmount * qty;
      }

      return {
        salesTaxAmount: parseFloat(salesTaxAmount.toFixed(2)),
        serviceTaxAmount: parseFloat(serviceTaxAmount.toFixed(2)),
        taxExclAmount: parseFloat(taxExclAmount.toFixed(2)),
        taxInclAmount: parseFloat(taxInclAmount.toFixed(2)),
        totalUnitAmountWOTax: parseFloat(totalUnitAmountWOTax.toFixed(2)),
        totalUnitAmount: parseFloat(totalUnitAmount.toFixed(2)),
        subTotalAmount: parseFloat(subTotalAmount.toFixed(2))
      };
    } catch (error) {
      console.error('Error calculating tax:', error);
      return {
        salesTaxAmount: 0,
        serviceTaxAmount: 0,
        taxExclAmount: 0,
        taxInclAmount: 0,
        totalUnitAmountWOTax: 0,
        totalUnitAmount: 0,
        subTotalAmount: 0
      };
    }
  }

  /**
   * Calculate transaction item for upsert operation
   * @param {Object} upsertTransactionItemRequest - Upsert transaction item request
   * @param {string} branchId - Branch ID
   * @returns {Promise<Object>} - Promise with transaction calculation result
   */
  async calculateUpsertTransactionItem(upsertTransactionItemRequest, branchId) {
    try {
      // Transform upsert request to regular transaction item request format
      const transactionItemRequest = {
        productId: upsertTransactionItemRequest.productId,
        unitAmount: upsertTransactionItemRequest.unitAmount,
        quantity: upsertTransactionItemRequest.quantity,
        totalUnitAmountWOTax: upsertTransactionItemRequest.totalUnitAmountWOTax,
        totalUnitAmount: upsertTransactionItemRequest.totalUnitAmount,
        subTotalAmount: upsertTransactionItemRequest.subTotalAmount
      };

      // Use the same calculation logic as regular transaction item
      return await this.calculateTransactionItem(transactionItemRequest, branchId);
    } catch (error) {
      console.error('Error calculating upsert transaction item:', error);
      throw error;
    }
  }

  /**
   * Validate transaction item request
   * @param {Object} transactionItemRequest - Transaction item request to validate
   * @returns {boolean} - Whether the request is valid
   */
  validateTransactionItemRequest(transactionItemRequest) {
    if (!transactionItemRequest) {
      return false;
    }

    const required = ['productId', 'unitAmount', 'quantity'];
    return required.every(field => transactionItemRequest[field] !== undefined && transactionItemRequest[field] !== null);
  }

  /**
   * Get default branch ID (fallback method)
   * @returns {string} - Default branch ID
   */
  getDefaultBranchId() {
    // This would typically come from user session or company settings
    // For now, return a default value
    return 'default-branch-id';
  }
}

// Export singleton instance
export default new TransactionService();
